import{a as Y,b as q,c as G,d as J,e as K,f as Q,g as W,h as Z,i as ee,j as ne,l as ie,m as re,n as oe,o as ae}from"./chunk-DZ52KWSJ.js";import{i as X,o as te,u as $}from"./chunk-3Y4XJPOZ.js";import{c as C,d as x,f as g,g as P}from"./chunk-PUAS7KUQ.js";import{Db as H,Eb as c,Fa as L,Fb as T,Gb as S,Lb as N,Mb as l,Nb as m,Ob as R,Wa as A,X as y,Ya as r,Za as u,bb as B,gc as j,hc as V,j as w,jb as d,jc as F,ka as E,kb as D,kc as z,lb as a,ma as k,pb as i,qb as o,rb as M,sb as O,ta as h,tb as I,ua as v,vb as U,wb as f,xb as _}from"./chunk-5P3CUSN4.js";var b=class t{constructor(n,e){this.el=n;this.renderer=e}highlightColor="rgba(6, 182, 212, 0.12)";scaleAmount="1.03";ngOnInit(){this.renderer.setStyle(this.el.nativeElement,"transition","all 0.3s ease")}onMouseEnter(){this.renderer.setStyle(this.el.nativeElement,"background-color",this.highlightColor),this.renderer.setStyle(this.el.nativeElement,"transform",`scale(${this.scaleAmount})`)}onMouseLeave(){this.renderer.setStyle(this.el.nativeElement,"background-color","transparent"),this.renderer.setStyle(this.el.nativeElement,"transform","scale(1)")}static \u0275fac=function(e){return new(e||t)(u(L),u(B))};static \u0275dir=k({type:t,selectors:[["","appHighlight",""]],hostBindings:function(e,s){e&1&&f("mouseenter",function(){return s.onMouseEnter()})("mouseleave",function(){return s.onMouseLeave()})},inputs:{highlightColor:"highlightColor",scaleAmount:"scaleAmount"},standalone:!0})};var se=C("fadeIn",[P(":enter",[g({opacity:0,transform:"translateY(20px)"}),x("300ms ease-in",g({opacity:1,transform:"translateY(0)"}))])]),Pe=C("slideIn",[P(":enter",[g({opacity:0,transform:"translateX(-20px)"}),x("400ms ease-out",g({opacity:1,transform:"translateX(0)"}))])]),ce=C("scaleIn",[P(":enter",[g({opacity:0,transform:"scale(0.8)"}),x("250ms ease-out",g({opacity:1,transform:"scale(1)"}))])]);function pe(t,n){t&1&&(i(0,"div",5),M(1,"mat-spinner",6),i(2,"p"),c(3,"Loading users..."),o()())}function ge(t,n){if(t&1){let e=U();i(0,"mat-card",11),f("click",function(){let p=h(e).$implicit,me=_(3);return v(me.onUserClick(p.id))}),i(1,"mat-card-content")(2,"div",12),M(3,"img",13),o(),i(4,"div",14)(5,"h3"),c(6),o(),i(7,"p",15),c(8),o()()()()}if(t&2){let e=n.$implicit;a("@scaleIn",void 0),D("data-user-id",e.id),r(3),a("src",e.image,A)("alt",e.firstName+" "+e.lastName),r(3),S("",e.firstName," ",e.lastName,""),r(2),T("ID: ",e.id,"")}}function ue(t,n){if(t&1&&(i(0,"div",9),d(1,ge,9,7,"mat-card",10),o()),t&2){let e=n.ngIf;a("@fadeIn",void 0),r(),a("ngForOf",e)}}function fe(t,n){if(t&1){let e=U();O(0),i(1,"button",17),l(2,"async"),f("click",function(){h(e);let p=_(3);return v(p.onPreviousPage())}),i(3,"mat-icon"),c(4,"chevron_left"),o(),c(5," Previous "),o(),i(6,"span",18),c(7),l(8,"async"),l(9,"async"),o(),i(10,"button",17),l(11,"async"),l(12,"async"),f("click",function(){h(e);let p=_(3);return v(p.onNextPage())}),c(13," Next "),i(14,"mat-icon"),c(15,"chevron_right"),o()(),I()}if(t&2){let e=_(3);r(),a("disabled",m(2,4,e.currentPage$)===1),r(6),S(" Page ",m(8,6,e.currentPage$)," of ",m(9,8,e.totalPages$)," "),r(3),a("disabled",m(11,10,e.currentPage$)===m(12,12,e.totalPages$))}}function _e(t,n){if(t&1&&(i(0,"div",16),d(1,fe,16,14,"ng-container",4),o()),t&2){let e=n.ngIf;r(),a("ngIf",e.length>0)}}function he(t,n){t&1&&(i(0,"div",19)(1,"p"),c(2,"No users found."),o()())}function ve(t,n){if(t&1&&(O(0),d(1,ue,2,2,"div",7),l(2,"async"),d(3,_e,2,1,"div",8),l(4,"async"),d(5,he,3,0,"ng-template",null,0,R),I()),t&2){let e=H(6),s=_();r(),a("ngIf",m(2,3,s.users$)),r(2),a("ngIf",m(4,5,s.users$))("ngIfElse",e)}}var le=class t{constructor(n,e){this.router=n;this.store=e;this.users$=this.store.select(ne),this.loading$=this.store.select(ie),this.currentPage$=this.store.select(re),this.totalPages$=this.store.select(oe),this.totalUsers$=this.store.select(ae)}users$;loading$;currentPage$;totalPages$;totalUsers$;destroy$=new w;usersPerPage=12;currentPage=1;ngOnInit(){this.loadUsers(),this.currentPage$.pipe(y(this.destroy$)).subscribe(n=>{this.currentPage=n})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadUsers(){this.store.dispatch($({page:this.currentPage,limit:this.usersPerPage}))}onUserClick(n){this.router.navigate(["/user",n])}onPageChange(n){n>=1&&this.store.dispatch($({page:n,limit:this.usersPerPage}))}onPreviousPage(){this.currentPage>1&&this.onPageChange(this.currentPage-1)}onNextPage(){this.totalPages$.pipe(y(this.destroy$)).subscribe(n=>{this.currentPage<n&&this.onPageChange(this.currentPage+1)})}static \u0275fac=function(e){return new(e||t)(u(X),u(te))};static \u0275cmp=E({type:t,selectors:[["app-user-list"]],standalone:!0,features:[N],decls:6,vars:6,consts:[["noUsers",""],[1,"user-list-container"],[1,"container"],["class","loading-container",4,"ngIf"],[4,"ngIf"],[1,"loading-container"],["diameter","50"],["class","user-grid",4,"ngIf"],["class","pagination",4,"ngIf","ngIfElse"],[1,"user-grid"],["class","user-card","appHighlight","","highlightColor","rgba(6, 182, 212, 0.12)","scaleAmount","1.05","animationDuration","0.3s","shadowIntensity","medium",3,"click",4,"ngFor","ngForOf"],["appHighlight","","highlightColor","rgba(6, 182, 212, 0.12)","scaleAmount","1.05","animationDuration","0.3s","shadowIntensity","medium",1,"user-card",3,"click"],[1,"user-avatar"],[3,"src","alt"],[1,"user-info"],[1,"user-id"],[1,"pagination"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"page-info"],[1,"no-users"]],template:function(e,s){e&1&&(i(0,"div",1)(1,"div",2),d(2,pe,4,0,"div",3),l(3,"async"),d(4,ve,7,7,"ng-container",4),l(5,"async"),o()()),e&2&&(r(2),a("ngIf",m(3,2,s.loading$)),r(2),a("ngIf",!m(5,4,s.loading$)))},dependencies:[z,j,V,F,G,Y,q,K,J,W,Q,ee,Z,b],styles:['.user-list-container[_ngcontent-%COMP%]{padding:2rem 0;min-height:60vh}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:3rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.user-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem;margin-bottom:2rem}.user-card[_ngcontent-%COMP%]{background:var(--gradient-surface);border-radius:16px;padding:1.5rem;box-shadow:var(--shadow-md);cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);border:2px solid var(--border-color);position:relative;overflow:hidden}.user-card[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:var(--gradient-primary);transform:scaleX(0);transition:transform .3s ease}.user-card[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.user-card[_ngcontent-%COMP%]:hover{transform:translateY(-6px);box-shadow:var(--shadow-xl);border-color:var(--primary-light);background:var(--surface-elevated)}.user-avatar[_ngcontent-%COMP%]{text-align:center;margin-bottom:1rem}.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid var(--border-color);transition:all .3s ease;box-shadow:var(--shadow-sm)}.user-card[_ngcontent-%COMP%]:hover   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:var(--primary-color);box-shadow:var(--shadow-md);transform:scale(1.05)}.user-info[_ngcontent-%COMP%]{text-align:center}.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:var(--text-primary);font-size:1.1rem;font-weight:600;transition:color .3s ease}.user-card[_ngcontent-%COMP%]:hover   .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--primary-color)}.user-id[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:.9rem;margin:0;font-weight:500}.pagination[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:1rem;margin-top:2rem}.page-info[_ngcontent-%COMP%]{font-weight:500;color:#333}.no-users[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#6c757d}@media (max-width: 768px){.user-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}.user-card[_ngcontent-%COMP%]{padding:1rem}.pagination[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}}'],data:{animation:[se,ce]}})};export{le as UserListComponent};

import{a as pe,f as be,p as N,q as he}from"./chunk-3Y4XJPOZ.js";import{Ab as Mt,B as Qt,Bb as Dt,Cb as Ct,D as wt,Da as k,Db as de,Ea as lt,F as et,Fa as _,J as Jt,Ja as oe,Ka as H,L as te,La as re,Lb as S,Ob as le,T as ee,U as ct,V as ie,Va as q,X as dt,Y as it,Ya as B,Yb as R,Za as d,Zb as nt,_a as se,a as h,aa as g,ba as p,da as f,dc as C,e as xt,fa as c,fb as ce,g as Yt,ga as v,gb as z,ic as me,j as tt,jb as Et,k as Kt,ka as T,kb as x,kc as pt,la as b,lb as at,ma as P,mb as mt,mc as ue,nb as E,ob as X,p as j,pb as A,q as Gt,qb as D,rb as W,t as M,u as Zt,ub as ut,va as kt,w as qt,wa as ae,xb as At,y as Xt,ya as ne,yb as $,zb as F}from"./chunk-5P3CUSN4.js";var Ft;try{Ft=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Ft=!1}var y=(()=>{class i{constructor(t){this._platformId=t,this.isBrowser=this._platformId?ue(this._platformId):typeof document=="object"&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!!(window.chrome||Ft)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}static{this.\u0275fac=function(e){return new(e||i)(c(oe))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var ot;function We(){if(ot==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>ot=!0}))}finally{ot=ot||!1}return ot}function Q(i){return We()?i:!!i.capture}var Tt;function $e(){if(Tt==null){let i=typeof document<"u"?document.head:null;Tt=!!(i&&(i.createShadowRoot||i.attachShadow))}return Tt}function fe(i){if($e()){let o=i.getRootNode?i.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&o instanceof ShadowRoot)return o}return null}function O(i){return i.composedPath?i.composedPath()[0]:i.target}function ge(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}function St(i){return Array.isArray(i)?i:[i]}function Y(i){return i instanceof _?i.nativeElement:i}var ve=new Set,K,Ye=(()=>{class i{constructor(t,e){this._platform=t,this._nonce=e,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):Ge}matchMedia(t){return(this._platform.WEBKIT||this._platform.BLINK)&&Ke(t,this._nonce),this._matchMedia(t)}static{this.\u0275fac=function(e){return new(e||i)(c(y),c(re,8))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function Ke(i,o){if(!ve.has(i))try{K||(K=document.createElement("style"),o&&K.setAttribute("nonce",o),K.setAttribute("type","text/css"),document.head.appendChild(K)),K.sheet&&(K.sheet.insertRule(`@media ${i} {body{ }}`,0),ve.add(i))}catch(t){console.error(t)}}function Ge(i){return{matches:i==="all"||i==="",media:i,addListener:()=>{},removeListener:()=>{}}}var ye=(()=>{class i{constructor(t,e){this._mediaMatcher=t,this._zone=e,this._queries=new Map,this._destroySubject=new tt}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(t){return _e(St(t)).some(a=>this._registerQuery(a).mql.matches)}observe(t){let a=_e(St(t)).map(r=>this._registerQuery(r).observable),n=Zt(a);return n=qt(n.pipe(et(1)),n.pipe(ct(1),wt(0))),n.pipe(M(r=>{let s={matches:!1,breakpoints:{}};return r.forEach(({matches:u,query:l})=>{s.matches=s.matches||u,s.breakpoints[l]=u}),s}))}_registerQuery(t){if(this._queries.has(t))return this._queries.get(t);let e=this._mediaMatcher.matchMedia(t),n={observable:new Yt(r=>{let s=u=>this._zone.run(()=>r.next(u));return e.addListener(s),()=>{e.removeListener(s)}}).pipe(ie(e),M(({matches:r})=>({query:t,matches:r})),dt(this._destroySubject)),mql:e};return this._queries.set(t,n),n}static{this.\u0275fac=function(e){return new(e||i)(c(Ye),c(k))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function _e(i){return i.map(o=>o.split(",")).reduce((o,t)=>o.concat(t)).map(o=>o.trim())}function Nt(i){return i.buttons===0||i.detail===0}function Ot(i){let o=i.touches&&i.touches[0]||i.changedTouches&&i.changedTouches[0];return!!o&&o.identifier===-1&&(o.radiusX==null||o.radiusX===1)&&(o.radiusY==null||o.radiusY===1)}var qe=new f("cdk-input-modality-detector-options"),Xe={ignoreKeys:[18,17,224,91,16]},we=650,J=Q({passive:!0,capture:!0}),Qe=(()=>{class i{get mostRecentModality(){return this._modality.value}constructor(t,e,a,n){this._platform=t,this._mostRecentTarget=null,this._modality=new Kt(null),this._lastTouchMs=0,this._onKeydown=r=>{this._options?.ignoreKeys?.some(s=>s===r.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=O(r))},this._onMousedown=r=>{Date.now()-this._lastTouchMs<we||(this._modality.next(Nt(r)?"keyboard":"mouse"),this._mostRecentTarget=O(r))},this._onTouchstart=r=>{if(Ot(r)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=O(r)},this._options=h(h({},Xe),n),this.modalityDetected=this._modality.pipe(ct(1)),this.modalityChanged=this.modalityDetected.pipe(Jt()),t.isBrowser&&e.runOutsideAngular(()=>{a.addEventListener("keydown",this._onKeydown,J),a.addEventListener("mousedown",this._onMousedown,J),a.addEventListener("touchstart",this._onTouchstart,J)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,J),document.removeEventListener("mousedown",this._onMousedown,J),document.removeEventListener("touchstart",this._onTouchstart,J))}static{this.\u0275fac=function(e){return new(e||i)(c(y),c(k),c(C),c(qe,8))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var ft=function(i){return i[i.IMMEDIATE=0]="IMMEDIATE",i[i.EVENTUAL=1]="EVENTUAL",i}(ft||{}),Je=new f("cdk-focus-monitor-default-options"),ht=Q({passive:!0,capture:!0}),ke=(()=>{class i{constructor(t,e,a,n,r){this._ngZone=t,this._platform=e,this._inputModalityDetector=a,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new tt,this._rootNodeFocusAndBlurListener=s=>{let u=O(s);for(let l=u;l;l=l.parentElement)s.type==="focus"?this._onFocus(s,l):this._onBlur(s,l)},this._document=n,this._detectionMode=r?.detectionMode||ft.IMMEDIATE}monitor(t,e=!1){let a=Y(t);if(!this._platform.isBrowser||a.nodeType!==1)return j();let n=fe(a)||this._getDocument(),r=this._elementInfo.get(a);if(r)return e&&(r.checkChildren=!0),r.subject;let s={checkChildren:e,subject:new tt,rootNode:n};return this._elementInfo.set(a,s),this._registerGlobalListeners(s),s.subject}stopMonitoring(t){let e=Y(t),a=this._elementInfo.get(e);a&&(a.subject.complete(),this._setClasses(e),this._elementInfo.delete(e),this._removeGlobalListeners(a))}focusVia(t,e,a){let n=Y(t),r=this._getDocument().activeElement;n===r?this._getClosestElementsInfo(n).forEach(([s,u])=>this._originChanged(s,e,u)):(this._setOrigin(e),typeof n.focus=="function"&&n.focus(a))}ngOnDestroy(){this._elementInfo.forEach((t,e)=>this.stopMonitoring(e))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(t){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(t)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:t&&this._isLastInteractionFromInputLabel(t)?"mouse":"program"}_shouldBeAttributedToTouch(t){return this._detectionMode===ft.EVENTUAL||!!t?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(t,e){t.classList.toggle("cdk-focused",!!e),t.classList.toggle("cdk-touch-focused",e==="touch"),t.classList.toggle("cdk-keyboard-focused",e==="keyboard"),t.classList.toggle("cdk-mouse-focused",e==="mouse"),t.classList.toggle("cdk-program-focused",e==="program")}_setOrigin(t,e=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=t,this._originFromTouchInteraction=t==="touch"&&e,this._detectionMode===ft.IMMEDIATE){clearTimeout(this._originTimeoutId);let a=this._originFromTouchInteraction?we:1;this._originTimeoutId=setTimeout(()=>this._origin=null,a)}})}_onFocus(t,e){let a=this._elementInfo.get(e),n=O(t);!a||!a.checkChildren&&e!==n||this._originChanged(e,this._getFocusOrigin(n),a)}_onBlur(t,e){let a=this._elementInfo.get(e);!a||a.checkChildren&&t.relatedTarget instanceof Node&&e.contains(t.relatedTarget)||(this._setClasses(e),this._emitOrigin(a,null))}_emitOrigin(t,e){t.subject.observers.length&&this._ngZone.run(()=>t.subject.next(e))}_registerGlobalListeners(t){if(!this._platform.isBrowser)return;let e=t.rootNode,a=this._rootNodeFocusListenerCount.get(e)||0;a||this._ngZone.runOutsideAngular(()=>{e.addEventListener("focus",this._rootNodeFocusAndBlurListener,ht),e.addEventListener("blur",this._rootNodeFocusAndBlurListener,ht)}),this._rootNodeFocusListenerCount.set(e,a+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(dt(this._stopInputModalityDetector)).subscribe(n=>{this._setOrigin(n,!0)}))}_removeGlobalListeners(t){let e=t.rootNode;if(this._rootNodeFocusListenerCount.has(e)){let a=this._rootNodeFocusListenerCount.get(e);a>1?this._rootNodeFocusListenerCount.set(e,a-1):(e.removeEventListener("focus",this._rootNodeFocusAndBlurListener,ht),e.removeEventListener("blur",this._rootNodeFocusAndBlurListener,ht),this._rootNodeFocusListenerCount.delete(e))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(t,e,a){this._setClasses(t,e),this._emitOrigin(a,e),this._lastFocusOrigin=e}_getClosestElementsInfo(t){let e=[];return this._elementInfo.forEach((a,n)=>{(n===t||a.checkChildren&&n.contains(t))&&e.push([n,a])}),e}_isLastInteractionFromInputLabel(t){let{_mostRecentTarget:e,mostRecentModality:a}=this._inputModalityDetector;if(a!=="mouse"||!e||e===t||t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA"||t.disabled)return!1;let n=t.labels;if(n){for(let r=0;r<n.length;r++)if(n[r].contains(e))return!0}return!1}static{this.\u0275fac=function(e){return new(e||i)(c(k),c(y),c(Qe),c(C,8),c(Je,8))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var G=function(i){return i[i.NONE=0]="NONE",i[i.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",i[i.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",i}(G||{}),Ie="cdk-high-contrast-black-on-white",xe="cdk-high-contrast-white-on-black",Rt="cdk-high-contrast-active",Ee=(()=>{class i{constructor(t,e){this._platform=t,this._document=e,this._breakpointSubscription=v(ye).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return G.NONE;let t=this._document.createElement("div");t.style.backgroundColor="rgb(1,2,3)",t.style.position="absolute",this._document.body.appendChild(t);let e=this._document.defaultView||window,a=e&&e.getComputedStyle?e.getComputedStyle(t):null,n=(a&&a.backgroundColor||"").replace(/ /g,"");switch(t.remove(),n){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return G.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return G.BLACK_ON_WHITE}return G.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let t=this._document.body.classList;t.remove(Rt,Ie,xe),this._hasCheckedHighContrastMode=!0;let e=this.getHighContrastMode();e===G.BLACK_ON_WHITE?t.add(Rt,Ie):e===G.WHITE_ON_BLACK&&t.add(Rt,xe)}}static{this.\u0275fac=function(e){return new(e||i)(c(y),c(C))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var Lt=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({})}}return i})();function ii(){return!0}var ai=new f("mat-sanity-checks",{providedIn:"root",factory:ii}),I=(()=>{class i{constructor(t,e,a){this._sanityChecks=e,this._document=a,this._hasDoneGlobalChecks=!1,t._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(t){return ge()?!1:typeof this._sanityChecks=="boolean"?this._sanityChecks:!!this._sanityChecks[t]}static{this.\u0275fac=function(e){return new(e||i)(c(Ee),c(ai,8),c(C))}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[Lt,Lt]})}}return i})();var w=function(i){return i[i.FADING_IN=0]="FADING_IN",i[i.VISIBLE=1]="VISIBLE",i[i.FADING_OUT=2]="FADING_OUT",i[i.HIDDEN=3]="HIDDEN",i}(w||{}),Bt=class{constructor(o,t,e,a=!1){this._renderer=o,this.element=t,this.config=e,this._animationForciblyDisabledThroughCss=a,this.state=w.HIDDEN}fadeOut(){this._renderer.fadeOutRipple(this)}},Ae=Q({passive:!0,capture:!0}),zt=class{constructor(){this._events=new Map,this._delegateEventHandler=o=>{let t=O(o);t&&this._events.get(o.type)?.forEach((e,a)=>{(a===t||a.contains(t))&&e.forEach(n=>n.handleEvent(o))})}}addHandler(o,t,e,a){let n=this._events.get(t);if(n){let r=n.get(e);r?r.add(a):n.set(e,new Set([a]))}else this._events.set(t,new Map([[e,new Set([a])]])),o.runOutsideAngular(()=>{document.addEventListener(t,this._delegateEventHandler,Ae)})}removeHandler(o,t,e){let a=this._events.get(o);if(!a)return;let n=a.get(t);n&&(n.delete(e),n.size===0&&a.delete(t),a.size===0&&(this._events.delete(o),document.removeEventListener(o,this._delegateEventHandler,Ae)))}},Me={enterDuration:225,exitDuration:150},ni=800,De=Q({passive:!0,capture:!0}),Ce=["mousedown","touchstart"],Te=["mouseup","mouseleave","touchend","touchcancel"],Ut=class i{static{this._eventManager=new zt}constructor(o,t,e,a){this._target=o,this._ngZone=t,this._platform=a,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,a.isBrowser&&(this._containerElement=Y(e))}fadeInRipple(o,t,e={}){let a=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),n=h(h({},Me),e.animation);e.centered&&(o=a.left+a.width/2,t=a.top+a.height/2);let r=e.radius||oi(o,t,a),s=o-a.left,u=t-a.top,l=n.enterDuration,m=document.createElement("div");m.classList.add("mat-ripple-element"),m.style.left=`${s-r}px`,m.style.top=`${u-r}px`,m.style.height=`${r*2}px`,m.style.width=`${r*2}px`,e.color!=null&&(m.style.backgroundColor=e.color),m.style.transitionDuration=`${l}ms`,this._containerElement.appendChild(m);let Vt=window.getComputedStyle(m),He=Vt.transitionProperty,Ht=Vt.transitionDuration,yt=He==="none"||Ht==="0s"||Ht==="0s, 0s"||a.width===0&&a.height===0,V=new Bt(this,m,e,yt);m.style.transform="scale3d(1, 1, 1)",V.state=w.FADING_IN,e.persistent||(this._mostRecentTransientRipple=V);let st=null;return!yt&&(l||n.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let Wt=()=>{st&&(st.fallbackTimer=null),clearTimeout($t),this._finishRippleTransition(V)},It=()=>this._destroyRipple(V),$t=setTimeout(It,l+100);m.addEventListener("transitionend",Wt),m.addEventListener("transitioncancel",It),st={onTransitionEnd:Wt,onTransitionCancel:It,fallbackTimer:$t}}),this._activeRipples.set(V,st),(yt||!l)&&this._finishRippleTransition(V),V}fadeOutRipple(o){if(o.state===w.FADING_OUT||o.state===w.HIDDEN)return;let t=o.element,e=h(h({},Me),o.config.animation);t.style.transitionDuration=`${e.exitDuration}ms`,t.style.opacity="0",o.state=w.FADING_OUT,(o._animationForciblyDisabledThroughCss||!e.exitDuration)&&this._finishRippleTransition(o)}fadeOutAll(){this._getActiveRipples().forEach(o=>o.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(o=>{o.config.persistent||o.fadeOut()})}setupTriggerEvents(o){let t=Y(o);!this._platform.isBrowser||!t||t===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=t,Ce.forEach(e=>{i._eventManager.addHandler(this._ngZone,e,t,this)}))}handleEvent(o){o.type==="mousedown"?this._onMousedown(o):o.type==="touchstart"?this._onTouchStart(o):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Te.forEach(t=>{this._triggerElement.addEventListener(t,this,De)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(o){o.state===w.FADING_IN?this._startFadeOutTransition(o):o.state===w.FADING_OUT&&this._destroyRipple(o)}_startFadeOutTransition(o){let t=o===this._mostRecentTransientRipple,{persistent:e}=o.config;o.state=w.VISIBLE,!e&&(!t||!this._isPointerDown)&&o.fadeOut()}_destroyRipple(o){let t=this._activeRipples.get(o)??null;this._activeRipples.delete(o),this._activeRipples.size||(this._containerRect=null),o===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),o.state=w.HIDDEN,t!==null&&(o.element.removeEventListener("transitionend",t.onTransitionEnd),o.element.removeEventListener("transitioncancel",t.onTransitionCancel),t.fallbackTimer!==null&&clearTimeout(t.fallbackTimer)),o.element.remove()}_onMousedown(o){let t=Nt(o),e=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+ni;!this._target.rippleDisabled&&!t&&!e&&(this._isPointerDown=!0,this.fadeInRipple(o.clientX,o.clientY,this._target.rippleConfig))}_onTouchStart(o){if(!this._target.rippleDisabled&&!Ot(o)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let t=o.changedTouches;if(t)for(let e=0;e<t.length;e++)this.fadeInRipple(t[e].clientX,t[e].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(o=>{let t=o.state===w.VISIBLE||o.config.terminateOnPointerUp&&o.state===w.FADING_IN;!o.config.persistent&&t&&o.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let o=this._triggerElement;o&&(Ce.forEach(t=>i._eventManager.removeHandler(t,o,this)),this._pointerUpEventsRegistered&&(Te.forEach(t=>o.removeEventListener(t,this,De)),this._pointerUpEventsRegistered=!1))}};function oi(i,o,t){let e=Math.max(Math.abs(i-t.left),Math.abs(i-t.right)),a=Math.max(Math.abs(o-t.top),Math.abs(o-t.bottom));return Math.sqrt(e*e+a*a)}var Ne=new f("mat-ripple-global-options"),ri=(()=>{class i{get disabled(){return this._disabled}set disabled(t){t&&this.fadeOutAllNonPersistent(),this._disabled=t,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(t){this._trigger=t,this._setupTriggerEventsIfEnabled()}constructor(t,e,a,n,r){this._elementRef=t,this._animationMode=r,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=n||{},this._rippleRenderer=new Ut(this,e,t,a)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:h(h(h({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(t,e=0,a){return typeof t=="number"?this._rippleRenderer.fadeInRipple(t,e,h(h({},this.rippleConfig),a)):this._rippleRenderer.fadeInRipple(0,0,h(h({},this.rippleConfig),t))}static{this.\u0275fac=function(e){return new(e||i)(d(_),d(k),d(y),d(Ne,8),d(H,8))}}static{this.\u0275dir=P({type:i,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(e,a){e&2&&E("mat-ripple-unbounded",a.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"],standalone:!0})}}return i})(),Oe=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[I,I]})}}return i})();var Fe={capture:!0},Se=["focus","mousedown","mouseenter","touchstart"],jt="mat-ripple-loader-uninitialized",Pt="mat-ripple-loader-class-name",Re="mat-ripple-loader-centered",gt="mat-ripple-loader-disabled",Le=(()=>{class i{constructor(){this._document=v(C,{optional:!0}),this._animationMode=v(H,{optional:!0}),this._globalRippleOptions=v(Ne,{optional:!0}),this._platform=v(y),this._ngZone=v(k),this._hosts=new Map,this._onInteraction=t=>{let e=O(t);if(e instanceof HTMLElement){let a=e.closest(`[${jt}="${this._globalRippleOptions?.namespace??""}"]`);a&&this._createRipple(a)}},this._ngZone.runOutsideAngular(()=>{for(let t of Se)this._document?.addEventListener(t,this._onInteraction,Fe)})}ngOnDestroy(){let t=this._hosts.keys();for(let e of t)this.destroyRipple(e);for(let e of Se)this._document?.removeEventListener(e,this._onInteraction,Fe)}configureRipple(t,e){t.setAttribute(jt,this._globalRippleOptions?.namespace??""),(e.className||!t.hasAttribute(Pt))&&t.setAttribute(Pt,e.className||""),e.centered&&t.setAttribute(Re,""),e.disabled&&t.setAttribute(gt,"")}getRipple(t){return this._hosts.get(t)||this._createRipple(t)}setDisabled(t,e){let a=this._hosts.get(t);if(a){a.disabled=e;return}e?t.setAttribute(gt,""):t.removeAttribute(gt)}_createRipple(t){if(!this._document)return;let e=this._hosts.get(t);if(e)return e;t.querySelector(".mat-ripple")?.remove();let a=this._document.createElement("span");a.classList.add("mat-ripple",t.getAttribute(Pt)),t.append(a);let n=new ri(new _(a),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return n._isInitialized=!0,n.trigger=t,n.centered=t.hasAttribute(Re),n.disabled=t.hasAttribute(gt),this.attachRipple(t,n),n}attachRipple(t,e){t.removeAttribute(jt),this._hosts.set(t,e)}destroyRipple(t){let e=this._hosts.get(t);e&&(e.ngOnDestroy(),this._hosts.delete(t))}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var si=["*"];var ci=new f("MAT_CARD_CONFIG"),hn=(()=>{class i{constructor(t){this.appearance=t?.appearance||"raised"}static{this.\u0275fac=function(e){return new(e||i)(d(ci,8))}}static{this.\u0275cmp=T({type:i,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(e,a){e&2&&E("mat-mdc-card-outlined",a.appearance==="outlined")("mdc-card--outlined",a.appearance==="outlined")},inputs:{appearance:"appearance"},exportAs:["matCard"],standalone:!0,features:[S],ngContentSelectors:si,decls:1,vars:0,template:function(e,a){e&1&&($(),F(0))},styles:['.mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-app-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-app-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-app-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-app-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:"";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-app-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-app-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-app-corner-medium));border-width:var(--mdc-outlined-card-outline-width);border-color:var(--mdc-outlined-card-outline-color, var(--mat-app-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-app-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-app-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-app-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-app-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-app-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-app-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-app-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-app-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-app-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-app-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-app-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-app-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}'],encapsulation:2,changeDetection:0})}}return i})();var fn=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275dir=P({type:i,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"],standalone:!0})}}return i})();var gn=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[I,pt,I]})}}return i})();var di=["mat-button",""],li=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],mi=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var ui=new f("MAT_BUTTON_CONFIG");var pi=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],bi=(()=>{class i{get ripple(){return this._rippleLoader?.getRipple(this._elementRef.nativeElement)}set ripple(t){this._rippleLoader?.attachRipple(this._elementRef.nativeElement,t)}get disableRipple(){return this._disableRipple}set disableRipple(t){this._disableRipple=t,this._updateRippleDisabled()}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._updateRippleDisabled()}constructor(t,e,a,n){this._elementRef=t,this._platform=e,this._ngZone=a,this._animationMode=n,this._focusMonitor=v(ke),this._rippleLoader=v(Le),this._isFab=!1,this._disableRipple=!1,this._disabled=!1;let r=v(ui,{optional:!0}),s=t.nativeElement,u=s.classList;this.disabledInteractive=r?.disabledInteractive??!1,this.color=r?.color??null,this._rippleLoader?.configureRipple(s,{className:"mat-mdc-button-ripple"});for(let{attribute:l,mdcClasses:m}of pi)s.hasAttribute(l)&&u.add(...m)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(t="program",e){t?this._focusMonitor.focusVia(this._elementRef.nativeElement,t,e):this._elementRef.nativeElement.focus(e)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static{this.\u0275fac=function(e){se()}}static{this.\u0275dir=P({type:i,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",R],disabled:[2,"disabled","disabled",R],ariaDisabled:[2,"aria-disabled","ariaDisabled",R],disabledInteractive:[2,"disabledInteractive","disabledInteractive",R]},features:[z]})}}return i})();var Tn=(()=>{class i extends bi{constructor(t,e,a,n){super(t,e,a,n)}static{this.\u0275fac=function(e){return new(e||i)(d(_),d(y),d(k),d(H,8))}}static{this.\u0275cmp=T({type:i,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(e,a){e&2&&(x("disabled",a._getDisabledAttribute())("aria-disabled",a._getAriaDisabled()),X(a.color?"mat-"+a.color:""),E("mat-mdc-button-disabled",a.disabled)("mat-mdc-button-disabled-interactive",a.disabledInteractive)("_mat-animation-noopable",a._animationMode==="NoopAnimations")("mat-unthemed",!a.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[ce,S],attrs:di,ngContentSelectors:mi,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(e,a){e&1&&($(li),W(0,"span",0),F(1),A(2,"span",1),F(3,1),D(),F(4,2),W(5,"span",2)(6,"span",3)),e&2&&E("mdc-button__ripple",!a._isFab)("mdc-fab__ripple",a._isFab)},styles:['.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 8px);height:var(--mdc-text-button-container-height);font-family:var(--mdc-text-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 8px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color)}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display)}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-app-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-filled-button-horizontal-padding, 16px);height:var(--mdc-filled-button-container-height);font-family:var(--mdc-filled-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color)}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-app-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-app-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-app-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color);background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-protected-button-horizontal-padding, 16px);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-app-level1));height:var(--mdc-protected-button-container-height);font-family:var(--mdc-protected-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color)}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-app-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-app-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-app-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-app-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-app-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color);background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-app-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-outlined-button-horizontal-padding, 15px);height:var(--mdc-outlined-button-container-height);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-app-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-app-corner-full));border-width:var(--mdc-outlined-button-outline-width)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color)}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-app-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-app-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color);border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button .mdc-button__ripple{border-width:var(--mdc-outlined-button-outline-width);border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px)*-1)}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0})}}return i})();var Fn=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[I,Oe,I]})}}return i})();var hi=["determinateSpinner"];function fi(i,o){if(i&1&&(kt(),A(0,"svg",11),W(1,"circle",12),D()),i&2){let t=At();x("viewBox",t._viewBox()),B(),mt("stroke-dasharray",t._strokeCircumference(),"px")("stroke-dashoffset",t._strokeCircumference()/2,"px")("stroke-width",t._circleStrokeWidth(),"%"),x("r",t._circleRadius())}}var gi=new f("mat-progress-spinner-default-options",{providedIn:"root",factory:vi});function vi(){return{diameter:je}}var je=100,_i=10,Wn=(()=>{class i{get color(){return this._color||this._defaultColor}set color(t){this._color=t}constructor(t,e,a){this._elementRef=t,this._defaultColor="primary",this._value=0,this._diameter=je,this._noopAnimations=e==="NoopAnimations"&&!!a&&!a._forceAnimations,this.mode=t.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",a&&(a.color&&(this.color=this._defaultColor=a.color),a.diameter&&(this.diameter=a.diameter),a.strokeWidth&&(this.strokeWidth=a.strokeWidth))}get value(){return this.mode==="determinate"?this._value:0}set value(t){this._value=Math.max(0,Math.min(100,t||0))}get diameter(){return this._diameter}set diameter(t){this._diameter=t||0}get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(t){this._strokeWidth=t||0}_circleRadius(){return(this.diameter-_i)/2}_viewBox(){let t=this._circleRadius()*2+this.strokeWidth;return`0 0 ${t} ${t}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static{this.\u0275fac=function(e){return new(e||i)(d(_),d(H,8),d(gi))}}static{this.\u0275cmp=T({type:i,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(e,a){if(e&1&&Mt(hi,5),e&2){let n;Dt(n=Ct())&&(a._determinateCircle=n.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(e,a){e&2&&(x("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",a.mode==="determinate"?a.value:null)("mode",a.mode),X("mat-"+a.color),mt("width",a.diameter,"px")("height",a.diameter,"px")("--mdc-circular-progress-size",a.diameter+"px")("--mdc-circular-progress-active-indicator-width",a.diameter+"px"),E("_mat-animation-noopable",a._noopAnimations)("mdc-circular-progress--indeterminate",a.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",nt],diameter:[2,"diameter","diameter",nt],strokeWidth:[2,"strokeWidth","strokeWidth",nt]},exportAs:["matProgressSpinner"],standalone:!0,features:[z,S],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(e,a){if(e&1&&(Et(0,fi,2,8,"ng-template",null,0,le),A(2,"div",2,1),kt(),A(4,"svg",3),W(5,"circle",4),D()(),ae(),A(6,"div",5)(7,"div",6)(8,"div",7),ut(9,8),D(),A(10,"div",9),ut(11,8),D(),A(12,"div",10),ut(13,8),D()()()),e&2){let n=de(1);B(4),x("viewBox",a._viewBox()),B(),mt("stroke-dasharray",a._strokeCircumference(),"px")("stroke-dashoffset",a._strokeDashOffset(),"px")("stroke-width",a._circleStrokeWidth(),"%"),x("r",a._circleRadius()),B(4),at("ngTemplateOutlet",n),B(2),at("ngTemplateOutlet",n),B(2),at("ngTemplateOutlet",n)}},dependencies:[me],styles:[".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-app-primary))}.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}"],encapsulation:2,changeDetection:0})}}return i})();var $n=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[pt,I]})}}return i})();var yi=["*"],vt;function Ii(){if(vt===void 0&&(vt=null,typeof window<"u")){let i=window;i.trustedTypes!==void 0&&(vt=i.trustedTypes.createPolicy("angular#components",{createHTML:o=>o}))}return vt}function rt(i){return Ii()?.createHTML(i)||i}function Pe(i){return Error(`Unable to find icon with the name "${i}"`)}function xi(){return Error("Could not find HttpClient for use with Angular Material icons. Please add provideHttpClient() to your providers.")}function Be(i){return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL via Angular's DomSanitizer. Attempted URL was "${i}".`)}function ze(i){return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by Angular's DomSanitizer. Attempted literal was "${i}".`)}var L=class{constructor(o,t,e){this.url=o,this.svgText=t,this.options=e}},wi=(()=>{class i{constructor(t,e,a,n){this._httpClient=t,this._sanitizer=e,this._errorHandler=n,this._svgIconConfigs=new Map,this._iconSetConfigs=new Map,this._cachedIconsByUrl=new Map,this._inProgressUrlFetches=new Map,this._fontCssClassesByAlias=new Map,this._resolvers=[],this._defaultFontSetClass=["material-icons","mat-ligature-font"],this._document=a}addSvgIcon(t,e,a){return this.addSvgIconInNamespace("",t,e,a)}addSvgIconLiteral(t,e,a){return this.addSvgIconLiteralInNamespace("",t,e,a)}addSvgIconInNamespace(t,e,a,n){return this._addSvgIconConfig(t,e,new L(a,null,n))}addSvgIconResolver(t){return this._resolvers.push(t),this}addSvgIconLiteralInNamespace(t,e,a,n){let r=this._sanitizer.sanitize(q.HTML,a);if(!r)throw ze(a);let s=rt(r);return this._addSvgIconConfig(t,e,new L("",s,n))}addSvgIconSet(t,e){return this.addSvgIconSetInNamespace("",t,e)}addSvgIconSetLiteral(t,e){return this.addSvgIconSetLiteralInNamespace("",t,e)}addSvgIconSetInNamespace(t,e,a){return this._addSvgIconSetConfig(t,new L(e,null,a))}addSvgIconSetLiteralInNamespace(t,e,a){let n=this._sanitizer.sanitize(q.HTML,e);if(!n)throw ze(e);let r=rt(n);return this._addSvgIconSetConfig(t,new L("",r,a))}registerFontClassAlias(t,e=t){return this._fontCssClassesByAlias.set(t,e),this}classNameForFontAlias(t){return this._fontCssClassesByAlias.get(t)||t}setDefaultFontSetClass(...t){return this._defaultFontSetClass=t,this}getDefaultFontSetClass(){return this._defaultFontSetClass}getSvgIconFromUrl(t){let e=this._sanitizer.sanitize(q.RESOURCE_URL,t);if(!e)throw Be(t);let a=this._cachedIconsByUrl.get(e);return a?j(_t(a)):this._loadSvgIconFromConfig(new L(t,null)).pipe(it(n=>this._cachedIconsByUrl.set(e,n)),M(n=>_t(n)))}getNamedSvgIcon(t,e=""){let a=Ue(e,t),n=this._svgIconConfigs.get(a);if(n)return this._getSvgFromConfig(n);if(n=this._getIconConfigFromResolvers(e,t),n)return this._svgIconConfigs.set(a,n),this._getSvgFromConfig(n);let r=this._iconSetConfigs.get(e);return r?this._getSvgFromIconSetConfigs(t,r):Gt(Pe(a))}ngOnDestroy(){this._resolvers=[],this._svgIconConfigs.clear(),this._iconSetConfigs.clear(),this._cachedIconsByUrl.clear()}_getSvgFromConfig(t){return t.svgText?j(_t(this._svgElementFromConfig(t))):this._loadSvgIconFromConfig(t).pipe(M(e=>_t(e)))}_getSvgFromIconSetConfigs(t,e){let a=this._extractIconWithNameFromAnySet(t,e);if(a)return j(a);let n=e.filter(r=>!r.svgText).map(r=>this._loadSvgIconSetFromConfig(r).pipe(Qt(s=>{let l=`Loading icon set URL: ${this._sanitizer.sanitize(q.RESOURCE_URL,r.url)} failed: ${s.message}`;return this._errorHandler.handleError(new Error(l)),j(null)})));return Xt(n).pipe(M(()=>{let r=this._extractIconWithNameFromAnySet(t,e);if(!r)throw Pe(t);return r}))}_extractIconWithNameFromAnySet(t,e){for(let a=e.length-1;a>=0;a--){let n=e[a];if(n.svgText&&n.svgText.toString().indexOf(t)>-1){let r=this._svgElementFromConfig(n),s=this._extractSvgIconFromSet(r,t,n.options);if(s)return s}}return null}_loadSvgIconFromConfig(t){return this._fetchIcon(t).pipe(it(e=>t.svgText=e),M(()=>this._svgElementFromConfig(t)))}_loadSvgIconSetFromConfig(t){return t.svgText?j(null):this._fetchIcon(t).pipe(it(e=>t.svgText=e))}_extractSvgIconFromSet(t,e,a){let n=t.querySelector(`[id="${e}"]`);if(!n)return null;let r=n.cloneNode(!0);if(r.removeAttribute("id"),r.nodeName.toLowerCase()==="svg")return this._setSvgAttributes(r,a);if(r.nodeName.toLowerCase()==="symbol")return this._setSvgAttributes(this._toSvgElement(r),a);let s=this._svgElementFromString(rt("<svg></svg>"));return s.appendChild(r),this._setSvgAttributes(s,a)}_svgElementFromString(t){let e=this._document.createElement("DIV");e.innerHTML=t;let a=e.querySelector("svg");if(!a)throw Error("<svg> tag not found");return a}_toSvgElement(t){let e=this._svgElementFromString(rt("<svg></svg>")),a=t.attributes;for(let n=0;n<a.length;n++){let{name:r,value:s}=a[n];r!=="id"&&e.setAttribute(r,s)}for(let n=0;n<t.childNodes.length;n++)t.childNodes[n].nodeType===this._document.ELEMENT_NODE&&e.appendChild(t.childNodes[n].cloneNode(!0));return e}_setSvgAttributes(t,e){return t.setAttribute("fit",""),t.setAttribute("height","100%"),t.setAttribute("width","100%"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("focusable","false"),e&&e.viewBox&&t.setAttribute("viewBox",e.viewBox),t}_fetchIcon(t){let{url:e,options:a}=t,n=a?.withCredentials??!1;if(!this._httpClient)throw xi();if(e==null)throw Error(`Cannot fetch icon from URL "${e}".`);let r=this._sanitizer.sanitize(q.RESOURCE_URL,e);if(!r)throw Be(e);let s=this._inProgressUrlFetches.get(r);if(s)return s;let u=this._httpClient.get(r,{responseType:"text",withCredentials:n}).pipe(M(l=>rt(l)),te(()=>this._inProgressUrlFetches.delete(r)),ee());return this._inProgressUrlFetches.set(r,u),u}_addSvgIconConfig(t,e,a){return this._svgIconConfigs.set(Ue(t,e),a),this}_addSvgIconSetConfig(t,e){let a=this._iconSetConfigs.get(t);return a?a.push(e):this._iconSetConfigs.set(t,[e]),this}_svgElementFromConfig(t){if(!t.svgElement){let e=this._svgElementFromString(t.svgText);this._setSvgAttributes(e,t.options),t.svgElement=e}return t.svgElement}_getIconConfigFromResolvers(t,e){for(let a=0;a<this._resolvers.length;a++){let n=this._resolvers[a](e,t);if(n)return ki(n)?new L(n.url,null,n.options):new L(n,null)}}static{this.\u0275fac=function(e){return new(e||i)(c(pe,8),c(be),c(C,8),c(lt))}}static{this.\u0275prov=g({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function _t(i){return i.cloneNode(!0)}function Ue(i,o){return i+":"+o}function ki(i){return!!(i.url&&i.options)}var Ei=new f("MAT_ICON_DEFAULT_OPTIONS"),Ai=new f("mat-icon-location",{providedIn:"root",factory:Mi});function Mi(){let i=v(C),o=i?i.location:null;return{getPathname:()=>o?o.pathname+o.search:""}}var Ve=["clip-path","color-profile","src","cursor","fill","filter","marker","marker-start","marker-mid","marker-end","mask","stroke"],Di=Ve.map(i=>`[${i}]`).join(", "),Ci=/^url\(['"]?#(.*?)['"]?\)$/,mo=(()=>{class i{get color(){return this._color||this._defaultColor}set color(t){this._color=t}get svgIcon(){return this._svgIcon}set svgIcon(t){t!==this._svgIcon&&(t?this._updateSvgIcon(t):this._svgIcon&&this._clearSvgElement(),this._svgIcon=t)}get fontSet(){return this._fontSet}set fontSet(t){let e=this._cleanupFontValue(t);e!==this._fontSet&&(this._fontSet=e,this._updateFontIconClasses())}get fontIcon(){return this._fontIcon}set fontIcon(t){let e=this._cleanupFontValue(t);e!==this._fontIcon&&(this._fontIcon=e,this._updateFontIconClasses())}constructor(t,e,a,n,r,s){this._elementRef=t,this._iconRegistry=e,this._location=n,this._errorHandler=r,this.inline=!1,this._previousFontSetClass=[],this._currentIconFetch=xt.EMPTY,s&&(s.color&&(this.color=this._defaultColor=s.color),s.fontSet&&(this.fontSet=s.fontSet)),a||t.nativeElement.setAttribute("aria-hidden","true")}_splitIconName(t){if(!t)return["",""];let e=t.split(":");switch(e.length){case 1:return["",e[0]];case 2:return e;default:throw Error(`Invalid icon name: "${t}"`)}}ngOnInit(){this._updateFontIconClasses()}ngAfterViewChecked(){let t=this._elementsWithExternalReferences;if(t&&t.size){let e=this._location.getPathname();e!==this._previousPath&&(this._previousPath=e,this._prependPathToReferences(e))}}ngOnDestroy(){this._currentIconFetch.unsubscribe(),this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear()}_usingFontIcon(){return!this.svgIcon}_setSvgElement(t){this._clearSvgElement();let e=this._location.getPathname();this._previousPath=e,this._cacheChildrenWithExternalReferences(t),this._prependPathToReferences(e),this._elementRef.nativeElement.appendChild(t)}_clearSvgElement(){let t=this._elementRef.nativeElement,e=t.childNodes.length;for(this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear();e--;){let a=t.childNodes[e];(a.nodeType!==1||a.nodeName.toLowerCase()==="svg")&&a.remove()}}_updateFontIconClasses(){if(!this._usingFontIcon())return;let t=this._elementRef.nativeElement,e=(this.fontSet?this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/):this._iconRegistry.getDefaultFontSetClass()).filter(a=>a.length>0);this._previousFontSetClass.forEach(a=>t.classList.remove(a)),e.forEach(a=>t.classList.add(a)),this._previousFontSetClass=e,this.fontIcon!==this._previousFontIconClass&&!e.includes("mat-ligature-font")&&(this._previousFontIconClass&&t.classList.remove(this._previousFontIconClass),this.fontIcon&&t.classList.add(this.fontIcon),this._previousFontIconClass=this.fontIcon)}_cleanupFontValue(t){return typeof t=="string"?t.trim().split(" ")[0]:t}_prependPathToReferences(t){let e=this._elementsWithExternalReferences;e&&e.forEach((a,n)=>{a.forEach(r=>{n.setAttribute(r.name,`url('${t}#${r.value}')`)})})}_cacheChildrenWithExternalReferences(t){let e=t.querySelectorAll(Di),a=this._elementsWithExternalReferences=this._elementsWithExternalReferences||new Map;for(let n=0;n<e.length;n++)Ve.forEach(r=>{let s=e[n],u=s.getAttribute(r),l=u?u.match(Ci):null;if(l){let m=a.get(s);m||(m=[],a.set(s,m)),m.push({name:r,value:l[1]})}})}_updateSvgIcon(t){if(this._svgNamespace=null,this._svgName=null,this._currentIconFetch.unsubscribe(),t){let[e,a]=this._splitIconName(t);e&&(this._svgNamespace=e),a&&(this._svgName=a),this._currentIconFetch=this._iconRegistry.getNamedSvgIcon(a,e).pipe(et(1)).subscribe(n=>this._setSvgElement(n),n=>{let r=`Error retrieving icon ${e}:${a}! ${n.message}`;this._errorHandler.handleError(new Error(r))})}}static{this.\u0275fac=function(e){return new(e||i)(d(_),d(wi),ne("aria-hidden"),d(Ai),d(lt),d(Ei,8))}}static{this.\u0275cmp=T({type:i,selectors:[["mat-icon"]],hostAttrs:["role","img",1,"mat-icon","notranslate"],hostVars:10,hostBindings:function(e,a){e&2&&(x("data-mat-icon-type",a._usingFontIcon()?"font":"svg")("data-mat-icon-name",a._svgName||a.fontIcon)("data-mat-icon-namespace",a._svgNamespace||a.fontSet)("fontIcon",a._usingFontIcon()?a.fontIcon:null),X(a.color?"mat-"+a.color:""),E("mat-icon-inline",a.inline)("mat-icon-no-color",a.color!=="primary"&&a.color!=="accent"&&a.color!=="warn"))},inputs:{color:"color",inline:[2,"inline","inline",R],svgIcon:"svgIcon",fontSet:"fontSet",fontIcon:"fontIcon"},exportAs:["matIcon"],standalone:!0,features:[z,S],ngContentSelectors:yi,decls:1,vars:0,template:function(e,a){e&1&&($(),F(0))},styles:["mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}"],encapsulation:2,changeDetection:0})}}return i})(),uo=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=b({type:i})}static{this.\u0275inj=p({imports:[I,I]})}}return i})();var Z=he("users"),ho=N(Z,i=>i.users),fo=N(Z,i=>i.selectedUser),go=N(Z,i=>i.loading),vo=N(Z,i=>i.error),Ti=N(Z,i=>i.currentPage),Fi=N(Z,i=>i.totalPages),Si=N(Z,i=>i.totalUsers),_o=N(Ti,Fi,Si,(i,o,t)=>({currentPage:i,totalPages:o,totalUsers:t}));export{hn as a,fn as b,gn as c,Tn as d,Fn as e,Wn as f,$n as g,mo as h,uo as i,ho as j,fo as k,go as l,Ti as m,Fi as n,Si as o};

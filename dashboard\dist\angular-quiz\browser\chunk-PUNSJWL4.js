import{a as w,b as A,c as B,d as N,e as $,f as T,g as j,h as R,i as V,k as G,l as q}from"./chunk-DZ52KWSJ.js";import{A as L,g as D,i as k,o as z,x as F}from"./chunk-3Y4XJPOZ.js";import{Eb as i,Fb as s,Gb as M,Lb as P,Mb as m,Nb as c,Wa as I,X as O,Ya as r,Za as u,hc as S,j as b,jb as p,jc as U,ka as y,kc as E,lb as l,pb as n,qb as t,rb as g,ta as _,ua as v,vb as C,wb as h,xb as f}from"./chunk-5P3CUSN4.js";function J(o,a){o&1&&(n(0,"div",5),g(1,"mat-spinner",6),n(2,"p"),i(3,"Loading user details..."),t()())}function K(o,a){if(o&1&&(n(0,"div",17)(1,"p")(2,"strong"),i(3,"Address:"),t()(),n(4,"p",18),i(5),g(6,"br"),i(7),g(8,"br"),i(9),t()()),o&2){let e=f().ngIf;r(5),s(" ",e.address.address,""),r(2),M(" ",e.address.city,", ",e.address.state,""),r(2),s(" ",e.address.country," ")}}function Q(o,a){if(o&1){let e=C();n(0,"div",7)(1,"button",8),h("click",function(){_(e);let x=f();return v(x.goBack())}),n(2,"mat-icon"),i(3,"arrow_back"),t(),i(4," Back to Users "),t(),n(5,"mat-card",9)(6,"mat-card-content")(7,"div",10)(8,"div",11),g(9,"img",12),t(),n(10,"div",13)(11,"h1"),i(12),t(),n(13,"p",14),i(14),t(),n(15,"div",15)(16,"p")(17,"strong"),i(18,"Email:"),t(),i(19),t(),n(20,"p")(21,"strong"),i(22,"Phone:"),t(),i(23),t(),n(24,"p")(25,"strong"),i(26,"Age:"),t(),i(27),t(),n(28,"p")(29,"strong"),i(30,"Gender:"),t(),i(31),t(),n(32,"p")(33,"strong"),i(34,"Birth Date:"),t(),i(35),t(),p(36,K,10,4,"div",16),t()()()()()()}if(o&2){let e=a.ngIf;r(9),l("src",e.image,I)("alt",e.firstName+" "+e.lastName),r(3),M("",e.firstName," ",e.lastName,""),r(2),s("User ID: ",e.id,""),r(5),s(" ",e.email||"N/A",""),r(4),s(" ",e.phone||"N/A",""),r(4),s(" ",e.age||"N/A",""),r(4),s(" ",e.gender||"N/A",""),r(4),s(" ",e.birthDate||"N/A",""),r(),l("ngIf",e.address)}}function W(o,a){if(o&1){let e=C();n(0,"div",19)(1,"h2"),i(2,"User Not Found"),t(),n(3,"p"),i(4,"The user you're looking for doesn't exist."),t(),n(5,"button",20),h("click",function(){_(e);let x=f();return v(x.goBack())}),n(6,"mat-icon"),i(7,"arrow_back"),t(),i(8," Back to Users "),t()()}}var H=class o{constructor(a,e,d){this.route=a;this.router=e;this.store=d;this.user$=this.store.select(G),this.loading$=this.store.select(q)}user$;loading$;destroy$=new b;userId=0;ngOnInit(){this.route.params.pipe(O(this.destroy$)).subscribe(a=>{this.userId=+a.id,this.loadUser()})}ngOnDestroy(){this.store.dispatch(L()),this.destroy$.next(),this.destroy$.complete()}loadUser(){this.store.dispatch(F({userId:this.userId}))}goBack(){this.router.navigate(["/users"])}static \u0275fac=function(e){return new(e||o)(u(D),u(k),u(z))};static \u0275cmp=y({type:o,selectors:[["app-user-detail"]],standalone:!0,features:[P],decls:10,vars:13,consts:[[1,"user-detail-container"],[1,"container"],["class","loading-container",4,"ngIf"],["class","user-detail",4,"ngIf"],["class","user-not-found",4,"ngIf"],[1,"loading-container"],["diameter","50"],[1,"user-detail"],["mat-raised-button","","color","primary",1,"back-btn",3,"click"],[1,"user-profile"],[1,"profile-layout"],[1,"user-avatar"],[3,"src","alt"],[1,"user-info"],[1,"user-id"],[1,"user-details"],["class","address-section",4,"ngIf"],[1,"address-section"],[1,"address-details"],[1,"user-not-found"],["mat-raised-button","","color","primary",3,"click"]],template:function(e,d){e&1&&(n(0,"div",0)(1,"div",1),p(2,J,4,0,"div",2),m(3,"async"),p(4,Q,37,11,"div",3),m(5,"async"),m(6,"async"),p(7,W,9,0,"div",4),m(8,"async"),m(9,"async"),t()()),e&2&&(r(2),l("ngIf",c(3,3,d.loading$)),r(2),l("ngIf",!c(5,5,d.loading$)&&c(6,7,d.user$)),r(3),l("ngIf",!c(8,9,d.loading$)&&!c(9,11,d.user$)))},dependencies:[E,S,U,B,w,A,$,N,j,T,V,R],styles:[".user-detail-container[_ngcontent-%COMP%]{padding:2rem 0;min-height:60vh}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:3rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.back-btn[_ngcontent-%COMP%]{margin-bottom:2rem}.user-profile[_ngcontent-%COMP%]{margin-top:1rem}.profile-layout[_ngcontent-%COMP%]{display:flex;gap:2rem;align-items:flex-start}.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:150px;height:150px;border-radius:50%;object-fit:cover;border:4px solid #e9ecef}.user-info[_ngcontent-%COMP%]{flex:1}.user-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#333;font-size:2rem}.user-id[_ngcontent-%COMP%]{color:#6c757d;font-size:1rem;margin-bottom:1.5rem}.user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;font-size:1rem;line-height:1.5}.user-details[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;min-width:80px;display:inline-block}.address-section[_ngcontent-%COMP%]{margin-top:1rem;padding-top:1rem;border-top:1px solid #e9ecef}.address-details[_ngcontent-%COMP%]{margin-left:80px;color:#6c757d;line-height:1.4}.user-not-found[_ngcontent-%COMP%]{text-align:center;padding:3rem;background:#fff;border-radius:12px;box-shadow:0 2px 8px #0000001a}.user-not-found[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#dc3545;margin-bottom:1rem}.user-not-found[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:2rem}@media (max-width: 768px){.profile-layout[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:1rem}.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:120px;height:120px}.user-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}}"]})};export{H as UserDetailComponent};

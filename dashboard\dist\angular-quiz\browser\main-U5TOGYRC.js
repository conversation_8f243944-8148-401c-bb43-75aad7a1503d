import{A as jt,B as Tt,a as Mt,b as Et,c as At,d as Ft,e as wt,h as xt,i as St,j as It,k as Ot,l as Pt,m as Nt,n as kt,o as K,r as Rt,s as v,t as Ut,u as X,v as J,w as ee,x as te,y as ne,z as R}from"./chunk-3Y4XJPOZ.js";import{$ as O,$a as rt,$b as E,A as H,B as g,Ca as q,D as Le,Da as Je,Ea as et,Eb as dt,F as We,Fa as Ve,G as ze,Hb as ht,I as qe,Ib as ft,J as Ze,Jb as pt,K as Qe,Ka as tt,Kb as k,Lb as F,Mb as gt,N as ve,Nb as mt,Q as Ye,Qb as vt,Ub as _t,W as _e,Wb as yt,X as Ke,Y as ye,Ya as nt,Yb as Ct,Za as c,_ as Ce,_a as it,_b as Q,a as s,aa as h,ab as ot,ac as Me,b as l,ba as $,bb as De,da as u,db as st,dc as bt,eb as Z,fa as _,fb as V,g as Ge,ga as d,hc as Vt,ia as Xe,j as C,jb as at,jc as Dt,k as Be,ka as A,kb as lt,kc as Y,la as L,lb as ct,ma as m,na as W,nb as ut,o as He,p as b,pb as D,qb as M,rb as P,sa as be,t as p,v as ge,wb as N,xa as z,y as $e,z as me}from"./chunk-5P3CUSN4.js";var Gt=[{path:"",redirectTo:"/users",pathMatch:"full"},{path:"users",loadComponent:()=>import("./chunk-JB3GDNEI.js").then(t=>t.UserListComponent)},{path:"user/:id",loadComponent:()=>import("./chunk-PUNSJWL4.js").then(t=>t.UserDetailComponent)},{path:"**",redirectTo:"/users"}];var En="@",An=(()=>{class t{constructor(n,i,r,o,a){this.doc=n,this.delegate=i,this.zone=r,this.animationType=o,this.moduleImpl=a,this._rendererFactoryPromise=null,this.scheduler=d(rt,{optional:!0}),this.loadingSchedulerFn=d(Fn,{optional:!0})}ngOnDestroy(){this._engine?.flush()}loadImpl(){let n=()=>this.moduleImpl??import("./chunk-NTDS2KH3.js").then(r=>r),i;return this.loadingSchedulerFn?i=this.loadingSchedulerFn(n):i=n(),i.catch(r=>{throw new Ce(5300,!1)}).then(({\u0275createEngine:r,\u0275AnimationRendererFactory:o})=>{this._engine=r(this.animationType,this.doc);let a=new o(this.delegate,this._engine,this.zone);return this.delegate=a,a})}createRenderer(n,i){let r=this.delegate.createRenderer(n,i);if(r.\u0275type===0)return r;typeof r.throwOnSyntheticProps=="boolean"&&(r.throwOnSyntheticProps=!1);let o=new Ee(r);return i?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(a=>{let f=a.createRenderer(n,i);o.use(f),this.scheduler?.notify(10)}).catch(a=>{o.use(r)}),o}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}static{this.\u0275fac=function(i){it()}}static{this.\u0275prov=h({token:t,factory:t.\u0275fac})}}return t})(),Ee=class{constructor(e){this.delegate=e,this.replay=[],this.\u0275type=1}use(e){if(this.delegate=e,this.replay!==null){for(let n of this.replay)n(e);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(e,n){return this.delegate.createElement(e,n)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}get destroyNode(){return this.delegate.destroyNode}appendChild(e,n){this.delegate.appendChild(e,n)}insertBefore(e,n,i,r){this.delegate.insertBefore(e,n,i,r)}removeChild(e,n,i){this.delegate.removeChild(e,n,i)}selectRootElement(e,n){return this.delegate.selectRootElement(e,n)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,n,i,r){this.delegate.setAttribute(e,n,i,r)}removeAttribute(e,n,i){this.delegate.removeAttribute(e,n,i)}addClass(e,n){this.delegate.addClass(e,n)}removeClass(e,n){this.delegate.removeClass(e,n)}setStyle(e,n,i,r){this.delegate.setStyle(e,n,i,r)}removeStyle(e,n,i){this.delegate.removeStyle(e,n,i)}setProperty(e,n,i){this.shouldReplay(n)&&this.replay.push(r=>r.setProperty(e,n,i)),this.delegate.setProperty(e,n,i)}setValue(e,n){this.delegate.setValue(e,n)}listen(e,n,i){return this.shouldReplay(n)&&this.replay.push(r=>r.listen(e,n,i)),this.delegate.listen(e,n,i)}shouldReplay(e){return this.replay!==null&&e.startsWith(En)}},Fn=new u("");function Bt(t="animations"){return st("NgAsyncAnimations"),W([{provide:ot,useFactory:(e,n,i)=>new An(e,n,i,t),deps:[bt,Ft,Je]},{provide:tt,useValue:t==="noop"?"NoopAnimations":"BrowserAnimations"}])}var wn={dispatch:!0,functional:!1,useEffectsErrorHandler:!0},ie="__@ngrx/effects_create__";function Ae(t,e={}){let n=e.functional?t:t(),i=s(s({},wn),e);return Object.defineProperty(n,ie,{value:i}),n}function xn(t){return Object.getOwnPropertyNames(t).filter(i=>t[i]&&t[i].hasOwnProperty(ie)?t[i][ie].hasOwnProperty("dispatch"):!1).map(i=>{let r=t[i][ie];return s({propertyName:i},r)})}function Sn(t){return xn(t)}function Ht(t){return Object.getPrototypeOf(t)}function In(t){return!!t.constructor&&t.constructor.name!=="Object"&&t.constructor.name!=="Function"}function $t(t){return typeof t=="function"}function On(t){return t.filter($t)}function Pn(t,e,n){let i=Ht(t),o=!!i&&i.constructor.name!=="Object"?i.constructor.name:null,a=Sn(t).map(({propertyName:f,dispatch:Vn,useEffectsErrorHandler:Dn})=>{let je=typeof t[f]=="function"?t[f]():t[f],Te=Dn?n(je,e):je;return Vn===!1?Te.pipe(ze()):Te.pipe(Ye()).pipe(p(Mn=>({effect:t[f],notification:Mn,propertyName:f,sourceName:o,sourceInstance:t})))});return me(...a)}var Nn=10;function Lt(t,e,n=Nn){return t.pipe(g(i=>(e&&e.handleError(i),n<=1?t:Lt(t,e,n-1))))}var Wt=(()=>{class t extends Ge{constructor(n){super(),n&&(this.source=n)}lift(n){let i=new t;return i.source=this,i.operator=n,i}static{this.\u0275fac=function(i){return new(i||t)(_(kt))}}static{this.\u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Fe(...t){return H(e=>t.some(n=>typeof n=="string"?n===e.type:n.type===e.type))}var er=new u("@ngrx/effects Internal Root Guard"),tr=new u("@ngrx/effects User Provided Effects"),nr=new u("@ngrx/effects Internal Root Effects"),ir=new u("@ngrx/effects Internal Root Effects Instances"),rr=new u("@ngrx/effects Internal Feature Effects"),or=new u("@ngrx/effects Internal Feature Effects Instance Groups"),kn=new u("@ngrx/effects Effects Error Handler",{providedIn:"root",factory:()=>Lt}),Rn="@ngrx/effects/init",Un=Ot(Rn);function jn(t,e){if(t.notification.kind==="N"){let n=t.notification.value;!Tn(n)&&e.handleError(new Error(`Effect ${Gn(t)} dispatched an invalid action: ${Bn(n)}`))}}function Tn(t){return typeof t!="function"&&t&&t.type&&typeof t.type=="string"}function Gn({propertyName:t,sourceInstance:e,sourceName:n}){let i=typeof e[t]=="function";return!!n?`"${n}.${String(t)}${i?"()":""}"`:`"${String(t)}()"`}function Bn(t){try{return JSON.stringify(t)}catch{return t}}var Hn="ngrxOnIdentifyEffects";function $n(t){return we(t,Hn)}var Ln="ngrxOnRunEffects";function Wn(t){return we(t,Ln)}var zn="ngrxOnInitEffects";function qn(t){return we(t,zn)}function we(t,e){return t&&e in t&&typeof t[e]=="function"}var zt=(()=>{class t extends C{constructor(n,i){super(),this.errorHandler=n,this.effectsErrorHandler=i}addEffects(n){this.next(n)}toActions(){return this.pipe(ve(n=>In(n)?Ht(n):n),ge(n=>n.pipe(ve(Zn))),ge(n=>{let i=n.pipe(Qe(o=>Qn(this.errorHandler,this.effectsErrorHandler)(o)),p(o=>(jn(o,this.errorHandler),o.notification)),H(o=>o.kind==="N"&&o.value!=null),qe()),r=n.pipe(We(1),H(qn),p(o=>o.ngrxOnInitEffects()));return me(i,r)}))}static{this.\u0275fac=function(i){return new(i||t)(_(et),_(kn))}}static{this.\u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Zn(t){return $n(t)?t.ngrxOnIdentifyEffects():""}function Qn(t,e){return n=>{let i=Pn(n,t,e);return Wn(n)?n.ngrxOnRunEffects(i):i}}var Yn=(()=>{class t{get isStarted(){return!!this.effectsSubscription}constructor(n,i){this.effectSources=n,this.store=i,this.effectsSubscription=null}start(){this.effectsSubscription||(this.effectsSubscription=this.effectSources.toActions().subscribe(this.store))}ngOnDestroy(){this.effectsSubscription&&(this.effectsSubscription.unsubscribe(),this.effectsSubscription=null)}static{this.\u0275fac=function(i){return new(i||t)(_(zt),_(K))}}static{this.\u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function qt(...t){let e=t.flat(),n=On(e);return W([n,{provide:Xe,multi:!0,useValue:()=>{d(Pt),d(Nt,{optional:!0});let i=d(Yn),r=d(zt),o=!i.isStarted;o&&i.start();for(let a of e){let f=$t(a)?d(a):a;r.addEffects(f)}o&&d(K).dispatch(Un())}}])}var Zt={users:[],selectedUser:null,loading:!1,error:null,currentPage:1,totalPages:1,totalUsers:0};var Qt=Ut(Zt,v(X,t=>l(s({},t),{loading:!0,error:null})),v(J,(t,{response:e,page:n})=>l(s({},t),{loading:!1,users:e.users.map(i=>({id:i.id,firstName:i.firstName,lastName:i.lastName,image:i.image})),currentPage:n,totalUsers:e.total,totalPages:Math.ceil(e.total/e.limit),error:null})),v(ee,(t,{error:e})=>l(s({},t),{loading:!1,error:e})),v(te,t=>l(s({},t),{loading:!0,error:null})),v(ne,(t,{user:e})=>l(s({},t),{loading:!1,selectedUser:e,error:null})),v(R,(t,{error:e})=>l(s({},t),{loading:!1,selectedUser:null,error:e})),v(jt,t=>l(s({},t),{selectedUser:null})),v(Tt,t=>l(s({},t),{error:null})));var re=class t{constructor(e){this.http=e}baseUrl="https://dummyjson.com";getUsers(e=12,n=0){let i=`${this.baseUrl}/users?limit=${e}&skip=${n}`;return this.http.get(i).pipe(g(r=>(console.error("Error fetching users:",r),b({users:[],total:0,skip:0,limit:0}))))}getUserById(e){let n=`${this.baseUrl}/users/${e}`;return this.http.get(n).pipe(g(i=>(console.error(`Error fetching user ${e}:`,i),b(null))))}searchUsers(e){let n=`${this.baseUrl}/users/search?q=${encodeURIComponent(e)}`;return this.http.get(n).pipe(g(i=>(console.error("Error searching users:",i),b({users:[],total:0,skip:0,limit:0}))))}static \u0275fac=function(n){return new(n||t)(_(Mt))};static \u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})};var w=class t{loadingSubject=new Be(!1);loading$=this.loadingSubject.asObservable();show(){this.loadingSubject.next(!0)}hide(){this.loadingSubject.next(!1)}static \u0275fac=function(n){return new(n||t)};static \u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})};var oe=class t{actions$=d(Wt);userService=d(re);loadingService=d(w);loadUsers$=Ae(()=>this.actions$.pipe(Fe(X),ye(()=>this.loadingService.show()),_e(({page:e,limit:n})=>{let i=(e-1)*n;return this.userService.getUsers(n,i).pipe(p(r=>(this.loadingService.hide(),J({response:r,page:e}))),g(r=>(this.loadingService.hide(),b(ee({error:r.message||"Failed to load users"})))))})));loadUserDetail$=Ae(()=>this.actions$.pipe(Fe(te),ye(()=>this.loadingService.show()),_e(({userId:e})=>this.userService.getUserById(e).pipe(p(n=>(this.loadingService.hide(),n?ne({user:n}):R({error:"User not found"}))),g(n=>(this.loadingService.hide(),b(R({error:n.message||"Failed to load user details"}))))))));static \u0275fac=function(n){return new(n||t)};static \u0275prov=h({token:t,factory:t.\u0275fac})};var Yt={providers:[_t({eventCoalescing:!0}),It(Gt),Et(At()),Bt(),Rt({users:Qt}),qt([oe])]};var an=(()=>{class t{constructor(n,i){this._renderer=n,this._elementRef=i,this.onChange=r=>{},this.onTouched=()=>{}}setProperty(n,i){this._renderer.setProperty(this._elementRef.nativeElement,n,i)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static{this.\u0275fac=function(i){return new(i||t)(c(De),c(Ve))}}static{this.\u0275dir=m({type:t})}}return t})(),ln=(()=>{class t extends an{static{this.\u0275fac=(()=>{let n;return function(r){return(n||(n=z(t)))(r||t)}})()}static{this.\u0275dir=m({type:t,features:[V]})}}return t})(),Pe=new u("");var Jn={provide:Pe,useExisting:O(()=>ue),multi:!0};function ei(){let t=Me()?Me().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var ti=new u(""),ue=(()=>{class t extends an{constructor(n,i,r){super(n,i),this._compositionMode=r,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!ei())}writeValue(n){let i=n??"";this.setProperty("value",i)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static{this.\u0275fac=function(i){return new(i||t)(c(De),c(Ve),c(ti,8))}}static{this.\u0275dir=m({type:t,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,r){i&1&&N("input",function(a){return r._handleInput(a.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(a){return r._compositionEnd(a.target.value)})},features:[k([Jn]),V]})}}return t})();function Kt(t){return t==null||(typeof t=="string"||Array.isArray(t))&&t.length===0}var cn=new u(""),ni=new u("");function ii(t){return e=>{if(Kt(e.value)||Kt(t))return null;let n=parseFloat(e.value);return!isNaN(n)&&n<t?{min:{min:t,actual:e.value}}:null}}function Xt(t){return null}function un(t){return t!=null}function dn(t){return vt(t)?He(t):t}function hn(t){let e={};return t.forEach(n=>{e=n!=null?s(s({},e),n):e}),Object.keys(e).length===0?null:e}function fn(t,e){return e.map(n=>n(t))}function ri(t){return!t.validate}function pn(t){return t.map(e=>ri(e)?e:n=>e.validate(n))}function oi(t){if(!t)return null;let e=t.filter(un);return e.length==0?null:function(n){return hn(fn(n,e))}}function gn(t){return t!=null?oi(pn(t)):null}function si(t){if(!t)return null;let e=t.filter(un);return e.length==0?null:function(n){let i=fn(n,e).map(dn);return $e(i).pipe(p(hn))}}function mn(t){return t!=null?si(pn(t)):null}function Jt(t,e){return t===null?[e]:Array.isArray(t)?[...t,e]:[t,e]}function ai(t){return t._rawValidators}function li(t){return t._rawAsyncValidators}function xe(t){return t?Array.isArray(t)?t:[t]:[]}function ae(t,e){return Array.isArray(t)?t.includes(e):t===e}function en(t,e){let n=xe(e);return xe(t).forEach(r=>{ae(n,r)||n.push(r)}),n}function tn(t,e){return xe(e).filter(n=>!ae(t,n))}var le=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=gn(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=mn(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,n){return this.control?this.control.hasError(e,n):!1}getError(e,n){return this.control?this.control.getError(e,n):null}},Se=class extends le{get formDirective(){return null}get path(){return null}},B=class extends le{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},Ie=class{constructor(e){this._cd=e}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},ci={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},no=l(s({},ci),{"[class.ng-submitted]":"isSubmitted"}),vn=(()=>{class t extends Ie{constructor(n){super(n)}static{this.\u0275fac=function(i){return new(i||t)(c(B,2))}}static{this.\u0275dir=m({type:t,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,r){i&2&&ut("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},features:[V]})}}return t})();var U="VALID",se="INVALID",x="PENDING",j="DISABLED",I=class{},ce=class extends I{constructor(e,n){super(),this.value=e,this.source=n}},T=class extends I{constructor(e,n){super(),this.pristine=e,this.source=n}},G=class extends I{constructor(e,n){super(),this.touched=e,this.source=n}},S=class extends I{constructor(e,n){super(),this.status=e,this.source=n}};function ui(t){return(de(t)?t.validators:t)||null}function di(t){return Array.isArray(t)?gn(t):t||null}function hi(t,e){return(de(e)?e.asyncValidators:t)||null}function fi(t){return Array.isArray(t)?mn(t):t||null}function de(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}var Oe=class{constructor(e,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Q(()=>this.statusReactive()),this.statusReactive=Z(void 0),this._pristine=Q(()=>this.pristineReactive()),this.pristineReactive=Z(!0),this._touched=Q(()=>this.touchedReactive()),this.touchedReactive=Z(!1),this._events=new C,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(e),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get status(){return E(this.statusReactive)}set status(e){E(()=>this.statusReactive.set(e))}get valid(){return this.status===U}get invalid(){return this.status===se}get pending(){return this.status==x}get disabled(){return this.status===j}get enabled(){return this.status!==j}get pristine(){return E(this.pristineReactive)}set pristine(e){E(()=>this.pristineReactive.set(e))}get dirty(){return!this.pristine}get touched(){return E(this.touchedReactive)}set touched(e){E(()=>this.touchedReactive.set(e))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(en(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(en(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(tn(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(tn(e,this._rawAsyncValidators))}hasValidator(e){return ae(this._rawValidators,e)}hasAsyncValidator(e){return ae(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){let n=this.touched===!1;this.touched=!0;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsTouched(l(s({},e),{sourceControl:i})),n&&e.emitEvent!==!1&&this._events.next(new G(!0,i))}markAllAsTouched(e={}){this.markAsTouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(e))}markAsUntouched(e={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:i})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,i),n&&e.emitEvent!==!1&&this._events.next(new G(!1,i))}markAsDirty(e={}){let n=this.pristine===!0;this.pristine=!1;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsDirty(l(s({},e),{sourceControl:i})),n&&e.emitEvent!==!1&&this._events.next(new T(!1,i))}markAsPristine(e={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:e.emitEvent})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e,i),n&&e.emitEvent!==!1&&this._events.next(new T(!0,i))}markAsPending(e={}){this.status=x;let n=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new S(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.markAsPending(l(s({},e),{sourceControl:n}))}disable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=j,this.errors=null,this._forEachChild(r=>{r.disable(l(s({},e),{onlySelf:!0}))}),this._updateValue();let i=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new ce(this.value,i)),this._events.next(new S(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(l(s({},e),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=U,this._forEachChild(i=>{i.enable(l(s({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(l(s({},e),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(e,n){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===U||this.status===x)&&this._runAsyncValidator(i,e.emitEvent)}let n=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new ce(this.value,n)),this._events.next(new S(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(l(s({},e),{sourceControl:n}))}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?j:U}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e,n){if(this.asyncValidator){this.status=x,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let i=dn(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:n,shouldHaveEmitted:e})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let e=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,e}return!1}setErrors(e,n={}){this.errors=e,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(e){let n=e;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((i,r)=>i&&i._find(r),this)}getError(e,n){let i=n?this.get(n):this;return i&&i.errors?i.errors[e]:null}hasError(e,n){return!!this.getError(e,n)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e,n,i){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),(e||i)&&this._events.next(new S(this.status,n)),this._parent&&this._parent._updateControlsErrors(e,n,i)}_initObservables(){this.valueChanges=new q,this.statusChanges=new q}_calculateStatus(){return this._allControlsDisabled()?j:this.errors?se:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(x)?x:this._anyControlsHaveStatus(se)?se:U}_anyControlsHaveStatus(e){return this._anyControls(n=>n.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e,n){let i=!this._anyControlsDirty(),r=this.pristine!==i;this.pristine=i,this._parent&&!e.onlySelf&&this._parent._updatePristine(e,n),r&&this._events.next(new T(this.pristine,n))}_updateTouched(e={},n){this.touched=this._anyControlsTouched(),this._events.next(new G(this.touched,n)),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,n)}_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){de(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let n=this._parent&&this._parent.dirty;return!e&&!!n&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=di(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=fi(this._rawAsyncValidators)}};var _n=new u("CallSetDisabledState",{providedIn:"root",factory:()=>Ne}),Ne="always";function pi(t,e){return[...e.path,t]}function gi(t,e,n=Ne){vi(t,e),e.valueAccessor.writeValue(t.value),(t.disabled||n==="always")&&e.valueAccessor.setDisabledState?.(t.disabled),_i(t,e),Ci(t,e),yi(t,e),mi(t,e)}function nn(t,e){t.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(e)})}function mi(t,e){if(e.valueAccessor.setDisabledState){let n=i=>{e.valueAccessor.setDisabledState(i)};t.registerOnDisabledChange(n),e._registerOnDestroy(()=>{t._unregisterOnDisabledChange(n)})}}function vi(t,e){let n=ai(t);e.validator!==null?t.setValidators(Jt(n,e.validator)):typeof n=="function"&&t.setValidators([n]);let i=li(t);e.asyncValidator!==null?t.setAsyncValidators(Jt(i,e.asyncValidator)):typeof i=="function"&&t.setAsyncValidators([i]);let r=()=>t.updateValueAndValidity();nn(e._rawValidators,r),nn(e._rawAsyncValidators,r)}function _i(t,e){e.valueAccessor.registerOnChange(n=>{t._pendingValue=n,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&yn(t,e)})}function yi(t,e){e.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&yn(t,e),t.updateOn!=="submit"&&t.markAsTouched()})}function yn(t,e){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function Ci(t,e){let n=(i,r)=>{e.valueAccessor.writeValue(i),r&&e.viewToModelUpdate(i)};t.registerOnChange(n),e._registerOnDestroy(()=>{t._unregisterOnChange(n)})}function bi(t,e){if(!t.hasOwnProperty("model"))return!1;let n=t.model;return n.isFirstChange()?!0:!Object.is(e,n.currentValue)}function Vi(t){return Object.getPrototypeOf(t.constructor)===ln}function Di(t,e){if(!e)return null;Array.isArray(e);let n,i,r;return e.forEach(o=>{o.constructor===ue?n=o:Vi(o)?i=o:r=o}),r||i||n||null}function rn(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function on(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var Mi=class extends Oe{constructor(e=null,n,i){super(ui(n),hi(i,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(e),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),de(n)&&(n.nonNullable||n.initialValueIsDefault)&&(on(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,n={}){this.value=this._pendingValue=e,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(i=>i(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(e,n={}){this.setValue(e,n)}reset(e=this.defaultValue,n={}){this._applyFormState(e),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){rn(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){rn(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){on(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var Ei={provide:B,useExisting:O(()=>ke)},sn=Promise.resolve(),ke=(()=>{class t extends B{constructor(n,i,r,o,a,f){super(),this._changeDetectorRef=a,this.callSetDisabledState=f,this.control=new Mi,this._registered=!1,this.name="",this.update=new q,this._parent=n,this._setValidators(i),this._setAsyncValidators(r),this.valueAccessor=Di(this,o)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let i=n.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),bi(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){gi(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){sn.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let i=n.isDisabled.currentValue,r=i!==0&&Ct(i);sn.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?pi(n,this._parent):[n]}static{this.\u0275fac=function(i){return new(i||t)(c(Se,9),c(cn,10),c(ni,10),c(Pe,10),c(yt,8),c(_n,8))}}static{this.\u0275dir=m({type:t,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[k([Ei]),V,be]})}}return t})();var Ai={provide:Pe,useExisting:O(()=>Re),multi:!0},Re=(()=>{class t extends ln{writeValue(n){let i=n??"";this.setProperty("value",i)}registerOnChange(n){this.onChange=i=>{n(i==""?null:parseFloat(i))}}static{this.\u0275fac=(()=>{let n;return function(r){return(n||(n=z(t)))(r||t)}})()}static{this.\u0275dir=m({type:t,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(i,r){i&1&&N("input",function(a){return r.onChange(a.target.value)})("blur",function(){return r.onTouched()})},features:[k([Ai]),V]})}}return t})();function Fi(t){return typeof t=="number"?t:parseFloat(t)}var wi=(()=>{class t{constructor(){this._validator=Xt}ngOnChanges(n){if(this.inputName in n){let i=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(i),this._validator=this._enabled?this.createValidator(i):Xt,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275dir=m({type:t,features:[be]})}}return t})();var xi={provide:cn,useExisting:O(()=>Ue),multi:!0},Ue=(()=>{class t extends wi{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=n=>Fi(n),this.createValidator=n=>ii(n)}static{this.\u0275fac=(()=>{let n;return function(r){return(n||(n=z(t)))(r||t)}})()}static{this.\u0275dir=m({type:t,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(i,r){i&2&&lt("min",r._enabled?r.min:null)},inputs:{min:"min"},features:[k([xi]),V]})}}return t})();var Si=(()=>{class t{static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275mod=L({type:t})}static{this.\u0275inj=$({})}}return t})();var Cn=(()=>{class t{static withConfig(n){return{ngModule:t,providers:[{provide:_n,useValue:n.callSetDisabledState??Ne}]}}static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275mod=L({type:t})}static{this.\u0275inj=$({imports:[Si]})}}return t})();var he=class t{constructor(e){this.router=e;this.searchSubject.pipe(Le(500),Ze(),Ke(this.destroy$)).subscribe(n=>{let i=parseInt(n,10);!isNaN(i)&&i>0&&(this.router.navigate(["/user",i]),this.searchQuery="")})}searchQuery="";searchSubject=new C;destroy$=new C;ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onSearchInput(e){let n=e.target;this.searchQuery=n.value,this.searchSubject.next(this.searchQuery)}static \u0275fac=function(n){return new(n||t)(c(St))};static \u0275cmp=A({type:t,selectors:[["app-header"]],standalone:!0,features:[F],decls:8,vars:1,consts:[[1,"header"],[1,"container"],[1,"header-content"],[1,"logo"],[1,"search-container"],[1,"search-box"],["type","number","placeholder","Search by user ID (e.g. 5)","autocomplete","off","min","1",1,"search-input",3,"ngModelChange","input","ngModel"]],template:function(n,i){n&1&&(D(0,"header",0)(1,"div",1)(2,"div",2)(3,"h1",3),dt(4,"Dashboard"),M(),D(5,"div",4)(6,"div",5)(7,"input",6),pt("ngModelChange",function(o){return ft(i.searchQuery,o)||(i.searchQuery=o),o}),N("input",function(o){return i.onSearchInput(o)}),M()()()()()()),n&2&&(nt(7),ht("ngModel",i.searchQuery))},dependencies:[Cn,ue,Re,vn,Ue,ke,Y],styles:[".header[_ngcontent-%COMP%]{background:var(--gradient-surface);box-shadow:var(--shadow-lg);padding:1.5rem 0;position:sticky;top:0;z-index:100;border-bottom:2px solid var(--border-color);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;gap:2rem}.logo[_ngcontent-%COMP%]{background:var(--gradient-primary);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-size:1.8rem;font-weight:700;margin:0;letter-spacing:-.5px;transition:all .3s ease}.logo[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,var(--primary-light) 0%,var(--secondary-color) 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;transform:scale(1.05)}.search-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;position:relative;max-width:400px;width:100%}.search-container[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%]:not(:first-of-type){display:none}.search-tip[_ngcontent-%COMP%]{display:none!important}.tip-content[_ngcontent-%COMP%]{background:var(--surface-color);border:2px solid var(--primary-color);border-radius:12px;padding:1rem;box-shadow:var(--shadow-lg);text-align:center}.tip-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .75rem;color:var(--text-primary);font-size:.9rem}.tip-close[_ngcontent-%COMP%]{background:var(--primary-color);color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;cursor:pointer;font-size:.85rem;font-weight:600}.tip-close[_ngcontent-%COMP%]:hover{background:var(--primary-dark)}.search-box[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--surface-color);border:2px solid var(--border-color);border-radius:12px;padding:.5rem;gap:.5rem;transition:all .3s ease}.search-box[_ngcontent-%COMP%]:focus-within{border-color:var(--primary-color);box-shadow:0 0 0 3px #6366f11a}.search-input[_ngcontent-%COMP%]{flex:1;padding:.75rem;border:none;outline:none;font-size:1rem;background:transparent;color:var(--text-primary)}.search-input[_ngcontent-%COMP%]::placeholder{color:var(--text-secondary)}.search-btn[_ngcontent-%COMP%], .clear-btn[_ngcontent-%COMP%], .help-btn[_ngcontent-%COMP%]{background:none;border:none;padding:.5rem;border-radius:6px;cursor:pointer;color:var(--text-secondary);transition:all .2s ease;display:flex;align-items:center;justify-content:center;min-width:32px;height:32px}.search-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:var(--primary-color);color:#fff}.search-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.clear-btn[_ngcontent-%COMP%]:hover{background:#ef4444;color:#fff}.spinner[_ngcontent-%COMP%]{width:16px;height:16px;border:2px solid #f3f3f3;border-top:2px solid var(--primary-color);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.results[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:var(--surface-color);border:2px solid var(--border-color);border-top:none;border-radius:0 0 12px 12px;box-shadow:var(--shadow-lg);z-index:1000;max-height:250px;overflow-y:auto}.result-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;cursor:pointer;transition:background-color .2s ease;border-bottom:1px solid var(--border-color)}.result-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.result-item[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,var(--primary-lightest) 0%,rgba(99,102,241,.05) 100%);transform:translate(4px);border-left:3px solid var(--primary-color);padding-left:calc(.75rem - 3px)}.result-item[_ngcontent-%COMP%]:hover   .user-name[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.result-item[_ngcontent-%COMP%]:hover   .user-pic[_ngcontent-%COMP%]{border-color:var(--primary-color);box-shadow:0 0 0 2px #6366f133}.user-pic[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;margin-right:.75rem;border:2px solid var(--border-color)}.user-info[_ngcontent-%COMP%]{flex:1}.user-name[_ngcontent-%COMP%]{font-weight:600;color:var(--text-primary);font-size:.9rem;margin-bottom:.25rem}.user-id[_ngcontent-%COMP%]{font-size:.8rem;color:var(--text-secondary)}.highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#06b6d4,#0891b2);color:#fff;padding:.15rem .4rem;border-radius:6px;font-weight:600;box-shadow:0 2px 8px #06b6d440,0 1px 3px #06b6d41a;animation:_ngcontent-%COMP%_modernHighlightPulse .5s ease-out;border:1px solid rgba(255,255,255,.2);-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}@keyframes _ngcontent-%COMP%_modernHighlightPulse{0%{background:linear-gradient(135deg,#06b6d4,#0891b2);transform:scale(1);box-shadow:0 2px 8px #06b6d440,0 1px 3px #06b6d41a}50%{background:linear-gradient(135deg,#0891b2,#0e7490);transform:scale(1.02);box-shadow:0 4px 16px #06b6d459,0 2px 6px #06b6d433}to{background:linear-gradient(135deg,#06b6d4,#0891b2);transform:scale(1);box-shadow:0 2px 8px #06b6d440,0 1px 3px #06b6d41a}}.no-results[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:var(--surface-color);border:2px solid var(--border-color);border-top:none;border-radius:0 0 12px 12px;box-shadow:var(--shadow-lg);z-index:1000;padding:1rem;text-align:center}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .75rem;color:var(--text-secondary)}.no-results[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:var(--primary-color);color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;cursor:pointer;font-size:.85rem}.no-results[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:var(--primary-dark)}.loading[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:var(--surface-color);border:2px solid var(--border-color);border-top:none;border-radius:0 0 12px 12px;box-shadow:var(--shadow-lg);z-index:1000;padding:1rem;text-align:center}.loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--text-secondary)}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.search-container[_ngcontent-%COMP%]{max-width:100%}}"]})};function Oi(t,e){t&1&&(D(0,"div",1),P(1,"div",2),M())}var fe=class t{constructor(e){this.loadingService=e}static \u0275fac=function(n){return new(n||t)(c(w))};static \u0275cmp=A({type:t,selectors:[["app-loading-bar"]],standalone:!0,features:[F],decls:2,vars:3,consts:[["class","loading-bar",4,"ngIf"],[1,"loading-bar"],[1,"loading-progress"]],template:function(n,i){n&1&&(at(0,Oi,2,0,"div",0),gt(1,"async")),n&2&&ct("ngIf",mt(1,1,i.loadingService.loading$))},dependencies:[Y,Vt,Dt],styles:[".loading-bar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:3px;background-color:#06b6d433;z-index:9999}.loading-progress[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#06b6d4,#0891b2);width:0%;animation:_ngcontent-%COMP%_loading 2s ease-in-out infinite}@keyframes _ngcontent-%COMP%_loading{0%{width:0%;margin-left:0%}50%{width:75%;margin-left:25%}to{width:0%;margin-left:100%}}"]})};var pe=class t{title="angular-quiz";static \u0275fac=function(n){return new(n||t)};static \u0275cmp=A({type:t,selectors:[["app-root"]],standalone:!0,features:[F],decls:4,vars:0,consts:[[1,"main-content"]],template:function(n,i){n&1&&(P(0,"app-loading-bar")(1,"app-header"),D(2,"main",0),P(3,"router-outlet"),M())},dependencies:[xt,he,fe],styles:[".main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 100px);background:linear-gradient(135deg,var(--background-color) 0%,#e2e8f0 100%)}"]})};wt(pe,Yt).catch(t=>console.error(t));
